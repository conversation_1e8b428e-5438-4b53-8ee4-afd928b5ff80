* DONE
** properly store the password in a cache in the daemon while running
  - proper authentication in the cli would be good too
** change the search results to a table form for easier reading in CLI
  - search text should list name, shortened description, and address
  - list pods should list name, address, creation date, and modified date
  - search by subject should list all things found with a predicate:object pattern
  - all other searches should return the raw output
  - have a raw output option for search
** fix the graph get_my_pods function, doesn't return everything in the graphs for some reason
** related to the get_my_pods issue, the text SPARQL query only returns things that it found, I want to return all of the things about subjects it found
** need to change the colony app take/restore pattern to do the same thing that is done in colony-daemon to prevent errors from crashing everything
** need to fix colony for the list_my_pods command format
** remove the pod_refs directory creation, it isn't used anywhere now
** add the ability to create a configuration pod with all of the default graph data and anything else
  - then when starting up or doing a refresh, pull this down first, get all the pod addresses from here
    and download all of them
  - then go through and download any references that you don't yet have
  - would be much faster when starting from scratch
** add a 'rename pod' function
** implement address count predicate/value in the configuration pod
  - write it to the configuration graph each time a new key is created and delete the old value
** when modifying a pod, delete the old "modified" date entries each so they don't accumulate
** need a way to recycle unused pointer and empty scratchpad addresses
  - update code to mark them as unused/empty
    - during remove_pod and put_subject_data when scratchpads become empty
  - then when calling add_pod/put_subject_data/add_pod_ref, check for empty/unused addresses first before creating a new one
    - really just modify the create_scratchpad and create_pointer functions to look first to grab them
  - when recycling, need to keep track of what kind of key it was in order to reuse the object at that address
** add a 'remove pod' function
  - clears out the current pod data
  - marks the pointer/scratchpad address as UNUSED in configuration graph
  - when creating a new pointer or new scratchpads, look to the UNUSED addresses
  - when refreshing, need to walk through newly unused addresses
    - if an unused address is a pod pointer, delete the file from the disk and clear out that graph
    - if a newly unused adress is a scratchpad, delete that file from the disk
  - add a check to make sure the configuration pod cannot be removed
** need a function to map the pointer and scratchpad addresses to the proper key_store pointers/scratchpads/bad array during refresh
  - any addresses not found in the configuration graph after fetching it during a refresh should be marked bad
** add $SECRET_KEY as a default for the wallet key in colony-daemon
  - look in the ant CLI and steal the code where it uses the wallet if it exists, else use $SECRET_KEY
  - priority:
    - key given at the user prompt
    - $SECRET_KEY environment variable
    - ant CLI wallet    
** need to figure out why that 'e' character is there when entering the password in colony-daemon
  - this is a library problem in how it interacts with the terminal apparently
** remove the address analysis commands when refreshing
  - everything is in the configuration, we know what addresses are used and their types
  - massive performance improvement
  - need to:
    - download the configuration pod pointer first
      - if not found error encountered, return from refresh operation with warning saying there was nothing to be found on the network
      - check if it is newer than the version in cache, if not return from refresh operation, else download the configuration pod scratchpads
    - create a method to find the pointer and scratchpad addresses from the configuration pod
    - if pointer is marked unused or scratchpad is empty, check to make sure they are empty locally, if not, clear them out
    - serial option:
      - walk through the pointers and download, download scratchpads if newer than what's in cache (already done)

** change the list my pods results to a table form for easier reading in CLI
  - list pods should list name, address, creation date, and modified date
** remove the legacy endpoint stuff from the colonyd
** update colonylib to add a name to the configuration pod, no reason this shouldn't have a name like everything else
** create function to return the list of pod addresses to be updated
  - return JSON string with 'update/add' or 'remove'
** test multi pod ref structure that it still works
  - seems to be a bug in the refresh_ref because it doesn't seem to catch everything on the first
  - it catches everything in multiple passes, need to investigate more thoroughly
        
* TODO


MSVCP104.dll, VCRUNTIME140.dll, and VCRUNTIME140_1.dll are missing

** add endpoint to return the list of pod addresses that will be updated, either added or removed
  - endpoint is added, need to update colony CLI to show the update when listing pods
** list pods still lists pod references
  - query looks correct, unsure why it lists them. need to debug more thoroughly here.

** test the various remove/rename scenarios and make sure it always works
** reupload the genesis pod with the latest schema on main net
** upload the colonyd and colony binaries to Autonomi and add them to the genesis pod
** create a multi-platform binary release process for colony

** multi-thread refresh function
  - create a task for each pointer and kick off concurrently
    - download pointer
    - if newer, download first scratchpad
    - create tasks for all additional scratchpads and download them concurrently
      - or maybe just serially. How often are people going to have multiple scratchpads per pod, probably skip this one for now unless its easy
** better handling for multi client scenarios
  - when creating a new pod locally, follow the normal procedure
  - when uploading, if the creation fails because there is something already there:
    - write the current graph for that pod into a temporary area
    - call the refresh command to make sure nothing else is missing and get the latest list of addresses
      - will wipe out the old data in the graph
    - get new addresses past the address count value
    - replace all of the old addresses in the temp graph file and create a new graph in the database
    - do this for all newly added pods
** multi-thread the refresh_ref function
  - Basically just need to spawn tasks for the download_referenced_pod() function call for each depth
** multi-thread upload_all function
  - spawn a task for each upload function call
** build test suite for creating pod references to make sure everything works
** add a "filter" predicate to colonylib to allow users to block a downstream reference pod from downloading or a subject/pod from showing up in searches
  - this attribute should pass through to all downstream users
  - there should be a 'block' and 'unblock' object definition, the closest one wins
** Add error handling to the Autonomi operations

* Things todo later
** add an abandon_pod function that permanently deletes everything
  - applies the ABANDONED attribute to the pointers and scratchpad addresses
    - basically the same as putting it into the 'bad' array
  - clears out the data in the graph
  - on upload, it will publish the data deletion just like remove_pod
** add the ability to change the cocoon password in colony-cli
** add the ability to change the wallet key in colony-cli
** merge the colonyd with the main colony app to enable running headless
  - and allow client interactions over REST API
