name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release (e.g., v1.0.0-test)'
        required: true
        default: 'v0.0.0-test'
        type: string
      draft:
        description: 'Create as draft release'
        required: false
        default: true
        type: boolean

env:
  CARGO_TERM_COLOR: always

jobs:
  checks:
    name: Run checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install frontend dependencies
        run: npm ci

      #FIXME: reenable this check when all errors/warnings are resolved
      #- name: Check frontend
      #  run: npm run check

      - name: Build frontend
        run: npm run build

      - name: Check Rust formatting
        run: cargo fmt --all --check
        working-directory: src-tauri

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings
        working-directory: src-tauri

      - name: Run Rust tests
        run: cargo test
        working-directory: src-tauri

  build:
    name: Build and Release
    needs: checks
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest'
            args: '--target universal-apple-darwin'
            target: 'universal-apple-darwin'
          - platform: 'ubuntu-22.04'
            args: '--target x86_64-unknown-linux-gnu'
            target: 'x86_64-unknown-linux-gnu'
          - platform: 'windows-latest'
            args: '--target x86_64-pc-windows-msvc'
            target: 'x86_64-pc-windows-msvc'

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install dependencies (Ubuntu only)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install frontend dependencies
        run: npm ci

      - name: Build frontend
        run: npm run build

      - name: Configure Windows static linking
        if: matrix.platform == 'windows-latest'
        run: |
          echo "RUSTFLAGS=-C target-feature=+crt-static" >> $GITHUB_ENV
        shell: bash

      - name: Build Tauri app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tagName: ${{ github.event.inputs.tag_name || github.ref_name }}
          releaseName: 'Colony ${{ github.event.inputs.tag_name || github.ref_name }}'
          releaseBody: |
            ## Colony v__VERSION__

            ### Installation

            **Linux:**
            - Download the `.AppImage` file for a portable application
            - Download the `.deb` file for Debian/Ubuntu systems

            **macOS:**
            - Download the `.dmg` file and drag Colony to your Applications folder

            **Windows:**
            - Download the `.msi` file and run the installer

            ### Changes
            See the commit history for detailed changes in this release.
          releaseDraft: ${{ github.event.inputs.draft || 'true' }}
          prerelease: false
          args: ${{ matrix.args }}
          bundleIdentifier: 'colony-app'
