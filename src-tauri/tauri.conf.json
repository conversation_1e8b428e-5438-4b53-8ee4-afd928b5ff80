{"$schema": "https://schema.tauri.app/config/2", "productName": "colony-app", "version": "0.1.0", "identifier": "colony-app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../build"}, "app": {"windows": [{"title": "colony-app", "width": 1024, "height": 600, "minWidth": 1024}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "linux": {"appimage": {"bundleMediaFramework": true}, "deb": {"depends": []}}, "macOS": {"dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"width": 660, "height": 400}}}, "windows": {"webviewInstallMode": {"type": "downloadBootstrapper"}, "nsis": {"installMode": "perMachine"}}}}